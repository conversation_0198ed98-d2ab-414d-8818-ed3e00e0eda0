FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements.txt with SpotDL dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

RUN echo "spotdl\nffmpeg-python\nyt-dlp>=2025.8.2\nrequests\nbeautifulsoup4\nmutagen\npydantic\npython-slugify\ntqdm\nflask" > requirements.txt

# Install Python dependencies
RUN pip install -r requirements.txt

# Verify yt-dlp
RUN yt-dlp --version

# Copy application code
COPY . .

# Create directories
RUN mkdir -p /app/music /app/sessions /app/user_settings /app/logs /app/static /app/templates

# Set environment variables
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PYTHONUNBUFFERED=1
ENV PRODUCTION=true

# Expose port
EXPOSE 5000

# Create non-root user, cache directory, and fix permissions
RUN useradd -m -u 1000 appuser && \
    mkdir -p /home/<USER>/.cache && \
    chown -R appuser:appuser /app /home/<USER>/.cache
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# Start application
CMD ["python", "app.py"]